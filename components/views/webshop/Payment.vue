<template>
	<Body class="page-webshop page-webshop-shipping page-checkout" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step3-col1">
					<WebshopStep :step="1" :completed="true" />
					<div class="wc-col-cnt">
						<BaseWebshopCheckout v-slot="{cart}">
							<div v-html="page.content" v-if="page?.content" v-interpolation />
							<BaseCmsLabel code="step2" tag="h1" class="wc-title" />
							<BaseWebshopShippingForm class="step3 step-form3 form form-animated-label ajax_siteform ajax_siteform_loading" v-slot="{fields, onShippingUpdate, status, activeShipping}">
								<div v-if="status?.data?.errors">
									<div class="error global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="shipping-options">
									<BaseCmsLabel code="checkout_shipping" tag="h2" class="wc-subtitle" v-interpolation />
									<div v-for="field in fields" :key="field.value">
										<BaseFormField :item="field" v-slot="{errorMessage}">
											<BaseFormInput :id="field.code" :option-class="'shipping-row' + (field?.length <= 1 ? ' single-shipping' : '')">
												<template #default="{item}">
													<span @click="onShippingUpdate(item)" v-html="item.title"></span>
													<div v-if="item.description" class="shipping_info" v-html="item.description"></div>
													<div class="shipping-data shipping_info shipping-info-parcels">
														<LazyBaseThemeWebshopGlsParcelLockers v-if="['gls_locker', 'gls_locker2'].includes(item.widget) && activeShipping.id == item.id" @select="onShippingUpdate(item, $event)" />
														<LazyBaseThemeWebshopBoxNowParcelLockers v-if="['boxnow_locker'].includes(item.widget) && activeShipping.id == item.id" @select="onShippingUpdate(item, $event)" />
													</div>
													<!-- FIXME - potrebno je proslijediti podatak vezan za SLIKU određenog načina dostave  -->
													<!-- <img src="/assets/images/card-small-si.png" width="129" height="80" alt="" />
													<?php if(!empty($shipping['image'])): ?> 
														<img src="/upload/<?php echo $shipping['image']; ?>" alt="<?php // echo $shipping['title']; ?>">
													<?php endif; ?> -->

													<BaseWebshopPickupLocations v-slot="{fields: locationFields, selectedLocation, onSelectLocation}" v-if="fields?.length && item.widget == 'location'">
														<div class="personal-pickup">
															<BaseFormField :item="locationFields[0]" v-slot="{errorMessage}">
																<BaseFormInput type="select" id="field-shipping_pickup_location" @change="onSelectLocation($event), onShippingUpdate(item)" :value="selectedLocation?.id ? selectedLocation.id : ''">
																	<option v-for="item in locationFields" :key="item.name" :value="item.value">{{ item.title }}</option>
																</BaseFormInput>
																<span class="error" v-show="errorMessage" v-html="errorMessage" />
															</BaseFormField>
															<div class="shipping-location" v-if="selectedLocation">
																<div class="shipping-location-address"><BaseCmsLabel code="address" tag="strong" />: <span class="address" v-if="selectedLocation.address" v-html="selectedLocation.address"></span></div>
															</div>
														</div>
													</BaseWebshopPickupLocations>
												</template>
											</BaseFormInput>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</BaseFormField>
									</div>
								</div>
							</BaseWebshopShippingForm>

							<BaseWebshopPaymentForm v-slot="{fields, loading2: loading, status, onPaymentUpdate}">
								<div v-if="status?.data?.errors">
									<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="payment-options">
									<BaseCmsLabel code="checkout_payment" tag="h2" class="wc-subtitle" v-interpolation />
									<div class="field-payment">
										<template v-for="field in fields" :key="field.value">
											<BaseFormField :item="field" v-slot="{errorMessage}">
												<BaseFormInput :id="field.code" option-class="payment-row">
													<template #default="{item}">
														<span @click="onPaymentUpdate(item)">{{item.title}}</span>
														<div v-if="item.description" class="payment_info" v-html="item.description"></div>
													</template>
												</BaseFormInput>

												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</BaseFormField>
										</template>
									</div>
								</div>
								<button class="btn btn-checkout btn-orange" type="submit" :class="{'loading': loading2}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" tag="span" /></button>
							</BaseWebshopPaymentForm>
						</BaseWebshopCheckout>
					</div>
					<WebshopStep :step="3" />
				</div>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>
