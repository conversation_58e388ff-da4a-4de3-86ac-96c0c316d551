<template>
	<div class="c-list-bottom">
		<div class="wrapper">
			<BaseUiSwiper
				class=""
				:options="{
					slidesPerView: 1,
					slidesPerGroup: 1,
					spaceBetween: 0,
					watchSlidesProgress: true,
					enabled: false,
					breakpoints: {
						900: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 5,
							watchSlidesProgress: true,
						},
						1250: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 5,
							watchSlidesProgress: true,
						}
					}
				}">
				<BaseUiSwiperSlide v-for="item in items" :key="item.id">
					<CatalogIndexEntry :item="item" itemListId="catalog_special_title" :itemListName="labels.get('catalog_special_title')" />
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['relatedItems', 'categoryPosition']);
	let items = props?.relatedItems ?? [];
	const categoryPosition = props?.categoryPosition;

	if(items?.length){
		items = items.filter(item => {
			const itemCategoryPosition = String(parseInt(item.category_position_h)).padStart(2, '0');
			return itemCategoryPosition === categoryPosition;
		});
	}
</script>
