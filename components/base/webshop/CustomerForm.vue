<!--
<PERSON><PERSON><PERSON> koriš<PERSON> s novom funkcionalnosti za uvjetno prikazivanje polja:

<BaseWebshopCustomerForm
	:location-config="{
		countries: [1, 5, 12]
	}"
	v-slot="{loading, fields, errors}">

</BaseWebshopCustomerForm>

Napomena:
- Države s ID-jevima 1, 5, 12 će imati prikazano location polje
- Ostale države će imati prikazana zipcode i city polja
- Automatski radi i za billing polja (field-b_location, field-b_zipcode, field-b_city)
-->

<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		{{ values }}
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const emit = defineEmits(['load', 'submit']);
	const {emit: globalEmit} = useEventBus();
	const config = useAppConfig();
	const auth = useAuth();
	const webshop = useWebshop();
	const {getAppUrl} = useApiRoutes();
	const dom = useDom();
	const labels = useLabels();
	const props = defineProps({
		submitUrl: {
			type: String,
			default: 'webshop_shipping',
		},
		changeFields: String,
		// Nova funkcionalnost: definiranje država koje trebaju location polje
		locationConfig: {
			type: Object,
			default: () => ({}),
			// Očekivani format:
			// {
			//   countries: [1, 5, 12] // ID-jevi država koje trebaju location polje
			// }
			// Ostale države će imati zipcode i city polja
		},
	});
	const status = ref(null);
	const loading = ref(false);

	// fetch form fields
	const fields = ref(null);

	// change fields types and validation if needed: https://hapi.marker.hr/#/Customer/get_v1_customer_fields_
	const changeFields = {};
	if (props.changeFields) {
		changeFields.change_fields = props.changeFields;
	}

	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'webshop.customer', ...changeFields}).then(res => res.data);

		// wait for next tick (DOM update) to make sure form is rendered
		await nextTick();

		// wrap r1 and company_oib fields
		wrapFields('field-group field-group-r1', ['b_r1', 'b_company_oib', 'b_company_name', 'b_company_address', 'b_company_zipcode', 'b_company_city']);

		// wrap b_shipping fields
		wrapFields('field-group field-group-shipping', ['b_same_as_shipping', 'b_first_name', 'b_last_name', 'b_address', 'b_zipcode', 'b_city', 'b_location', 'b_phone', 'b_house_number', 'b_country']);

		// setup country field visibility logic
		setupCountryFieldVisibility();

		// setup zipcode/city to location mapping
		setupZipcodeLocationMapping();

		// set focus on first field in form. "data-autofocus" attribute needs to be set on form element
		dom.setFieldFocus();

		// emit event when form is loaded. Can be used to trigger analytics event or similar
		emit('load');
	});

	async function onSubmit({values, actions}) {
		loading.value = true;
		const formValues = values;
		if (props.changeFields) {
			formValues._change_fields = props.changeFields;
		}

		const res = await webshop.submitCustomerData(formValues);
		await webshop.fetchCustomer();
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		emit('submit', res);

		// if no api errors, navigate to next step
		if (res?.success && !res.data?.errors?.length && props.submitUrl) {
			return navigateTo(getAppUrl(props.submitUrl));
		}
		loading.value = false;
	}

	// wrap related fields with subfields
	function wrapFields(cssClass, relatedFields) {
		const rootField = document.getElementById(relatedFields[0]);
		if (!rootField) return;

		// get root element
		const rootElement = rootField.parentElement.parentElement;

		// create wrapper div
		let wrapperDiv = document.createElement('div');
		wrapperDiv.className = cssClass;

		// Move elements to the wrapper
		const relatedField = document.getElementById(relatedFields[0]).parentElement;
		rootElement.insertBefore(wrapperDiv, relatedField);
		relatedFields.forEach(field => {
			const el = document.querySelector(`input[name="${field}"], select[name="${field}"]`);
			if (el) wrapperDiv.appendChild(el.parentElement);

			// add event listener to main field
			if (field == relatedFields[0]) {
				el.addEventListener('change', function () {
					wrapperDiv.classList.toggle('active');
				});

				// set active class on load if checkbox is checked
				if (el.name == 'b_r1' && el.checked) wrapperDiv.classList.add('active');
				if (el.name == 'b_same_as_shipping' && !el.checked) wrapperDiv.classList.add('active');
			}
		});
	}

	// Nova funkcionalnost: setup country field visibility logic
	function setupCountryFieldVisibility() {
		// Provjeri da li je locationConfig definiran
		if (!props.locationConfig || !props.locationConfig.countries || !Array.isArray(props.locationConfig.countries)) {
			return;
		}

		// Pronađi country select polja
		const countryField = document.querySelector('select[name="country"]');
		const bCountryField = document.querySelector('select[name="b_country"]');

		// Dodaj event listenere za promjenu države i postavi početno stanje
		if (countryField) {
			countryField.addEventListener('change', () => handleCountryChange(countryField.value));
			// Postavi početno stanje - provjeri value ili selected opciju
			const initialValue = getInitialCountryValue(countryField);
			if (initialValue) {
				handleCountryChange(initialValue, '', false); // false = ne briši vrijednosti na početku
			}
		}

		if (bCountryField) {
			bCountryField.addEventListener('change', () => handleCountryChange(bCountryField.value, 'b_'));
			// Postavi početno stanje - provjeri value ili selected opciju
			const initialValue = getInitialCountryValue(bCountryField);
			if (initialValue) {
				handleCountryChange(initialValue, 'b_', false); // false = ne briši vrijednosti na početku
			}
		}
	}

	// Funkcija za dobivanje inicijalne vrijednosti države
	function getInitialCountryValue(selectElement) {
		// Prvo provjeri value atribut
		if (selectElement.value) {
			return selectElement.value;
		}

		// Zatim provjeri selected opciju
		const selectedOption = selectElement.querySelector('option[selected]');
		if (selectedOption) {
			return selectedOption.value;
		}

		return null;
	}

	// Funkcija za rukovanje promjenom države
	function handleCountryChange(countryValue, prefix = '', clearValues = true) {
		globalEmit('selectedCountry', countryValue);

		// Konvertiraj countryValue u broj za usporedbu
		const countryId = parseInt(countryValue);

		// Provjeri da li država treba location polje
		const needsLocation = props.locationConfig.countries.includes(countryId);

		// Definiraj selektore za polja
		const locationSelector = prefix ? `.field-${prefix}location` : '.field-location';
		const zipcodeSelector = prefix ? `.field-${prefix}zipcode` : '.field-zipcode';
		const citySelector = prefix ? `.field-${prefix}city` : '.field-city';

		// Pronađi elemente
		const locationField = document.querySelector(locationSelector);
		const zipcodeField = document.querySelector(zipcodeSelector);
		const cityField = document.querySelector(citySelector);

		// Obriši vrijednosti sva tri polja samo ako je clearValues true
		if (clearValues) {
			const locationInput = locationField?.querySelector('input');
			const zipcodeInput = zipcodeField?.querySelector('input');
			const cityInput = cityField?.querySelector('input');

			if (locationInput) locationInput.value = '';
			if (zipcodeInput) zipcodeInput.value = '';
			if (cityInput) cityInput.value = '';
		}

		if (needsLocation) {
			// Prikaži location polje, sakrij zipcode i city
			if (locationField) locationField.removeAttribute('hidden');
			if (zipcodeField) zipcodeField.setAttribute('hidden', '');
			if (cityField) cityField.setAttribute('hidden', '');
		} else {
			// Sakrij location polje, prikaži zipcode i city
			if (locationField) locationField.setAttribute('hidden', '');
			if (zipcodeField) zipcodeField.removeAttribute('hidden');
			if (cityField) cityField.removeAttribute('hidden');
		}
	}
</script>

<style>
	.field-group > *:not(:first-child) {
		display: none;
	}
	.field-group.active > *:not(:first-child) {
		display: block;
	}
</style>
